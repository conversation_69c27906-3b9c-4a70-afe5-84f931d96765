// Simple in-memory cache utility
class SimpleCache {
  private cache = new Map<string, { data: any; timestamp: number }>()
  private defaultTTL = 30 * 1000 // 30 seconds

  set(key: string, data: any, ttl?: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now() + (ttl || this.defaultTTL)
    })
  }

  get(key: string): any | null {
    const item = this.cache.get(key)
    if (!item) return null

    if (Date.now() > item.timestamp) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  delete(key: string): void {
    this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  // Clean expired entries
  cleanup(): void {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now > item.timestamp) {
        this.cache.delete(key)
      }
    }
  }

  // Get cache stats
  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
}

// Global cache instance
export const cache = new SimpleCache()

// Cleanup expired entries every 5 minutes
if (typeof window === 'undefined') { // Server-side only
  setInterval(() => {
    cache.cleanup()
  }, 5 * 60 * 1000)
}

// Cache key generators
export const cacheKeys = {
  dashboardStats: (userId: string, role: string) => `dashboard-stats-${userId}-${role}`,
  notifications: (userId: string) => `notifications-${userId}`,
  meetings: (userId: string) => `meetings-${userId}`,
  freelancers: () => 'freelancers-list',
  userProfile: (userId: string) => `user-profile-${userId}`,
}
