import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { cache, cacheKeys } from "@/lib/cache"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const cacheKey = cacheKeys.userProfile(session.user.id)
    
    // Check cache first
    const cached = cache.get(cacheKey)
    if (cached) {
      return NextResponse.json({ profile: cached })
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
        updatedAt: true,
        // Include profile data based on role
        freelancerProfile: session.user.role === "FREELANCER" ? {
          select: {
            bio: true,
            skills: true,
            hourlyRate: true,
            experience: true,
            portfolio: true,
            availability: true,
            isActive: true,
          }
        } : false,
        clientProfile: session.user.role === "CLIENT" ? {
          select: {
            company: true,
            industry: true,
            bio: true,
          }
        } : false,
      }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Cache the result
    cache.set(cacheKey, user)

    return NextResponse.json({ profile: user })
  } catch (error) {
    console.error("Error fetching user profile:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { name, email, bio, company, industry, skills, hourlyRate, experience, portfolio, availability, profileImage, backgroundBanner } = body

    // Validate required fields
    if (!name || !email) {
      return NextResponse.json(
        { error: "Name and email are required" },
        { status: 400 }
      )
    }

    // Check if email is already taken by another user
    if (email !== session.user.email) {
      const existingUser = await prisma.user.findUnique({
        where: { email },
        select: { id: true }
      })

      if (existingUser && existingUser.id !== session.user.id) {
        return NextResponse.json(
          { error: "Email is already taken" },
          { status: 400 }
        )
      }
    }

    // Update user basic info
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: {
        name,
        email,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        updatedAt: true,
      }
    })

    // Update role-specific profile
    if (session.user.role === "FREELANCER") {
      // Update or create freelancer profile
      await prisma.freelancerProfile.upsert({
        where: { userId: session.user.id },
        update: {
          bio: bio || null,
          skills: skills || [],
          hourlyRate: hourlyRate ? parseFloat(hourlyRate) : null,
          experience: experience || null,
          portfolio: portfolio || null,
          availability: availability || null,
          profileImage: profileImage || null,
          backgroundBanner: backgroundBanner || null,
        },
        create: {
          userId: session.user.id,
          bio: bio || null,
          skills: skills || [],
          hourlyRate: hourlyRate ? parseFloat(hourlyRate) : null,
          experience: experience || null,
          portfolio: portfolio || null,
          availability: availability || null,
          profileImage: profileImage || null,
          backgroundBanner: backgroundBanner || null,
          title: "Freelancer",
          description: "Professional freelancer",
          isActive: true,
        }
      })
    } else if (session.user.role === "CLIENT") {
      // Update or create client profile
      await prisma.clientProfile.upsert({
        where: { userId: session.user.id },
        update: {
          company: company || null,
          industry: industry || null,
          bio: bio || null,
          profileImage: profileImage || null,
          backgroundBanner: backgroundBanner || null,
        },
        create: {
          userId: session.user.id,
          company: company || null,
          industry: industry || null,
          bio: bio || null,
          profileImage: profileImage || null,
          backgroundBanner: backgroundBanner || null,
        }
      })
    }

    // Invalidate cache
    const cacheKey = cacheKeys.userProfile(session.user.id)
    cache.delete(cacheKey)

    return NextResponse.json({ 
      message: "Profile updated successfully",
      profile: updatedUser
    })
  } catch (error) {
    console.error("Error updating user profile:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
