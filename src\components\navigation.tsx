"use client"

import Link from "next/link"
import { useSession, signOut } from "next-auth/react"
import { Button } from "@/components/ui/button"
import { User, LogOut, Calendar, Users } from "lucide-react"
import Image from "next/image"

export function Navigation() {
  const { data: session, status } = useSession()

  // Don't render during loading to prevent hydration issues
  if (status === "loading") {
    return null
  }

  // Only show navigation on non-dashboard pages
  if (session && typeof window !== 'undefined' && window.location.pathname.startsWith('/dashboard')) {
    return null
  }

  return (
    <nav className="border-b bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link href="/" className="text-xl font-bold text-primary">
              ClearAway
            </Link>
          </div>

          <div className="flex items-center space-x-4">
            {session ? (
              <>
                <Link href="/dashboard">
                  <Button variant="ghost" size="sm" className="flex items-center gap-2">
                    {session.user.image ? (
                      <div className="relative w-4 h-4 rounded-full overflow-hidden">
                        <Image
                          src={session.user.image}
                          alt={session.user.name || "User"}
                          fill
                          className="object-cover"
                        />
                      </div>
                    ) : (
                      <User className="h-4 w-4" />
                    )}
                    Dashboard
                  </Button>
                </Link>

                <Link href="/dashboard/projects">
                  <Button variant="ghost" size="sm">
                    Projects
                  </Button>
                </Link>

                <Link href="/dashboard/verification">
                  <Button variant="ghost" size="sm">
                    Verification
                  </Button>
                </Link>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => signOut({ callbackUrl: "/" })}
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign Out
                </Button>
              </>
            ) : (
              <>
                <Link href="/auth/signin">
                  <Button variant="ghost" size="sm">
                    Sign In
                  </Button>
                </Link>
                <Link href="/auth/signup">
                  <Button size="sm">
                    Sign Up
                  </Button>
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  )
}
