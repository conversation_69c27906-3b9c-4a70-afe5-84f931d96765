import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    console.log(`📅 Fetching meeting history for user: ${session.user.id} (${session.user.name})`)

    // Build where clause based on user role
    const whereClause = session.user.role === "CLIENT"
      ? { clientId: session.user.id }
      : { freelancerId: session.user.id }

    console.log("📅 Where clause:", whereClause)

    // Get meetings with related data
    const meetings = await prisma.meeting.findMany({
      where: {
        ...whereClause,
        // Only include completed or cancelled meetings for history
        status: {
          in: ['COMPLETED', 'CANCELLED']
        }
      },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        freelancer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        // Include payments if they exist
        payments: {
          select: {
            id: true,
            amount: true,
            status: true,
            createdAt: true
          }
        }
      },
      orderBy: {
        scheduledAt: 'desc'
      }
    })

    console.log(`📅 Found ${meetings.length} historical meetings for user ${session.user.name}`)

    // Format meetings for the frontend
    const formattedMeetings = meetings.map(meeting => {
      // Determine the other party based on current user role
      const otherParty = session.user.role === "CLIENT" 
        ? meeting.freelancer 
        : meeting.client

      return {
        id: meeting.id,
        title: meeting.title,
        description: meeting.description,
        scheduledAt: meeting.scheduledAt,
        duration: meeting.duration,
        status: meeting.status,
        type: meeting.type,
        meetingUrl: meeting.meetingUrl,
        notes: meeting.notes,
        clientDecision: meeting.clientDecision,
        createdAt: meeting.createdAt,
        updatedAt: meeting.updatedAt,
        // Add the other party info (freelancer for clients, client for freelancers)
        freelancer: session.user.role === "CLIENT" ? {
          id: meeting.freelancer?.id,
          name: meeting.freelancer?.name,
          email: meeting.freelancer?.email
        } : {
          id: meeting.client?.id,
          name: meeting.client?.name,
          email: meeting.client?.email
        },
        // Include payment information
        payments: meeting.payments || []
      }
    })

    return NextResponse.json({ 
      meetings: formattedMeetings,
      total: formattedMeetings.length
    })

  } catch (error) {
    console.error("Error fetching meeting history:", error)
    return NextResponse.json(
      { error: "Failed to fetch meeting history" },
      { status: 500 }
    )
  }
}
