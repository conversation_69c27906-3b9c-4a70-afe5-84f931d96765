"use client"

import { useState, useRef, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Upload, X, Camera, Image as ImageIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import Image from "next/image"

interface ImageUploadProps {
  currentImage?: string | null
  onImageChange: (imageUrl: string | null) => void
  type: "profile" | "banner"
  className?: string
  disabled?: boolean
}

export function ImageUpload({ 
  currentImage, 
  onImageChange, 
  type, 
  className,
  disabled = false 
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileUpload = useCallback(async (file: File) => {
    if (!file) return

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      alert('Please upload a valid image file (JPEG, PNG, or WebP)')
      return
    }

    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB')
      return
    }

    setIsUploading(true)

    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', type)

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
      })

      if (response.ok) {
        const data = await response.json()
        onImageChange(data.imageUrl)
      } else {
        const errorData = await response.json()
        alert(errorData.error || 'Failed to upload image')
      }
    } catch (error) {
      console.error('Error uploading image:', error)
      alert('Failed to upload image')
    } finally {
      setIsUploading(false)
    }
  }, [type, onImageChange])

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      handleFileUpload(file)
    }
  }

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)
    
    const file = e.dataTransfer.files?.[0]
    if (file) {
      handleFileUpload(file)
    }
  }, [handleFileUpload])

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)
  }

  const handleRemoveImage = async () => {
    if (currentImage) {
      try {
        await fetch(`/api/upload/image?imageUrl=${encodeURIComponent(currentImage)}`, {
          method: 'DELETE',
        })
      } catch (error) {
        console.error('Error deleting image:', error)
      }
    }
    onImageChange(null)
  }

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  const isProfile = type === "profile"
  const aspectRatio = isProfile ? "aspect-square" : "aspect-[3/1]"
  const dimensions = isProfile ? "w-32 h-32" : "w-full h-32"

  return (
    <div className={cn("space-y-4", className)}>
      <Card className={cn(
        "relative overflow-hidden transition-all duration-200",
        dragActive && "border-primary bg-primary/5",
        disabled && "opacity-50 cursor-not-allowed"
      )}>
        <CardContent className="p-0">
          <div
            className={cn(
              "relative group cursor-pointer",
              dimensions,
              aspectRatio,
              "bg-muted flex items-center justify-center"
            )}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={!disabled ? openFileDialog : undefined}
          >
            {currentImage ? (
              <>
                <Image
                  src={currentImage}
                  alt={`${type} image`}
                  fill
                  className="object-cover"
                  sizes={isProfile ? "128px" : "100vw"}
                />
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={(e) => {
                        e.stopPropagation()
                        openFileDialog()
                      }}
                      disabled={disabled || isUploading}
                    >
                      <Camera className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleRemoveImage()
                      }}
                      disabled={disabled || isUploading}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center">
                {isUploading ? (
                  <div className="flex flex-col items-center gap-2">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <p className="text-sm text-muted-foreground">Uploading...</p>
                  </div>
                ) : (
                  <div className="flex flex-col items-center gap-2">
                    {isProfile ? (
                      <Camera className="h-8 w-8 text-muted-foreground" />
                    ) : (
                      <ImageIcon className="h-8 w-8 text-muted-foreground" />
                    )}
                    <div className="text-sm text-muted-foreground text-center">
                      <p>Click to upload or drag and drop</p>
                      <p className="text-xs">PNG, JPG, WebP up to 5MB</p>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled || isUploading}
      />

      {!currentImage && !isUploading && (
        <Button
          variant="outline"
          onClick={openFileDialog}
          disabled={disabled}
          className="w-full"
        >
          <Upload className="h-4 w-4 mr-2" />
          Upload {isProfile ? "Profile Picture" : "Banner Image"}
        </Button>
      )}
    </div>
  )
}
