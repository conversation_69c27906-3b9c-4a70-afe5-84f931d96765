// Import Prisma Client
import { PrismaClient } from '@prisma/client'

// Declare global variable for Prisma
declare global {
  var __prisma: PrismaClient | undefined
}

// Initialize Prisma Client with optimized configuration
let prisma: PrismaClient

if (process.env.NODE_ENV === 'production') {
  prisma = new PrismaClient({
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
    // Optimize connection pooling for production
    log: ['error'],
  })
} else {
  if (!global.__prisma) {
    global.__prisma = new PrismaClient({
      log: process.env.DEBUG_QUERIES === 'true' ? ['query', 'error', 'warn'] : ['error', 'warn'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL,
        },
      },
    })
  }
  prisma = global.__prisma
}

// Graceful shutdown
process.on('beforeExit', async () => {
  await prisma.$disconnect()
})

export { prisma }
