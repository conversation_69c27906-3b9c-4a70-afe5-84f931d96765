"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Shield, 
  Mail, 
  Phone, 
  FileText, 
  User,
  AlertCircle,
  ExternalLink
} from "lucide-react"

interface VerificationData {
  verification: {
    id: string
    userId: string
    idVerification: 'UNVERIFIED' | 'PENDING' | 'VERIFIED' | 'REJECTED'
    emailVerified: boolean
    phoneVerified: boolean
    portfolioVerified: boolean
    backgroundCheck: 'UNVERIFIED' | 'PENDING' | 'VERIFIED' | 'REJECTED'
    verifiedAt?: string
    createdAt: string
    updatedAt: string
  }
  metrics: {
    verificationScore: number
    totalChecks: number
    progressPercentage: number
    isFullyVerified: boolean
    trustLevel: 'UNVERIFIED' | 'PARTIALLY_VERIFIED' | 'VERIFIED' | 'HIGHLY_TRUSTED'
  }
}

export default function VerificationPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [verificationData, setVerificationData] = useState<VerificationData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (status === "loading") return
    if (!session) {
      router.push("/auth/signin")
      return
    }
    
    fetchVerificationStatus()
  }, [session, status, router])

  const fetchVerificationStatus = async () => {
    setIsLoading(true)
    setError(null)
    try {
      const response = await fetch("/api/verification/status")
      if (response.ok) {
        const data = await response.json()
        setVerificationData(data)
      } else {
        // Handle non-200 responses
        try {
          const errorData = await response.json()
          console.log("API Error Response:", errorData)

          if (response.status === 404 && errorData.code === "USER_NOT_FOUND") {
            setError("Your account needs to be refreshed. Please sign out and sign in again to continue.")
          } else {
            setError(errorData.error || `Failed to load verification status (${response.status})`)
          }
        } catch (parseError) {
          // If we can't parse the error response
          setError(`Failed to load verification status (${response.status})`)
        }
      }
    } catch (networkError) {
      console.error("Network error fetching verification status:", networkError)
      setError("Network error: Failed to connect to verification service")
    } finally {
      setIsLoading(false)
    }
  }

  const submitVerification = async (verificationType: string) => {
    setIsSubmitting(true)
    try {
      const response = await fetch("/api/verification/submit", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ verificationType }),
      })

      if (response.ok) {
        await fetchVerificationStatus() // Refresh data
      }
    } catch (error) {
      console.error("Error submitting verification:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const getStatusIcon = (status: string | boolean) => {
    if (status === true || status === 'VERIFIED') {
      return <CheckCircle className="h-5 w-5 text-green-500" />
    } else if (status === 'PENDING') {
      return <Clock className="h-5 w-5 text-yellow-500" />
    } else if (status === 'REJECTED') {
      return <XCircle className="h-5 w-5 text-red-500" />
    } else {
      return <AlertCircle className="h-5 w-5 text-gray-400" />
    }
  }

  const getStatusText = (status: string | boolean) => {
    if (status === true || status === 'VERIFIED') return 'Verified'
    if (status === 'PENDING') return 'Pending'
    if (status === 'REJECTED') return 'Rejected'
    return 'Not Started'
  }

  const getTrustLevelColor = (level: string) => {
    switch (level) {
      case 'HIGHLY_TRUSTED': return 'bg-green-500'
      case 'VERIFIED': return 'bg-blue-500'
      case 'PARTIALLY_VERIFIED': return 'bg-yellow-500'
      default: return 'bg-gray-400'
    }
  }

  if (status === "loading" || isLoading) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>
  }

  if (!session) {
    return null
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="max-w-md mx-auto">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Account Refresh Required</h2>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button
            onClick={() => window.location.href = "/api/auth/signout"}
            className="bg-red-500 hover:bg-red-600"
          >
            Sign Out & Refresh Account
          </Button>
        </div>
      </div>
    )
  }

  if (!verificationData) {
    return <div className="text-center py-12">Error loading verification data</div>
  }

  const { verification, metrics } = verificationData

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Account Verification</h1>
        <p className="text-muted-foreground">
          Complete your verification to build trust and unlock more opportunities
        </p>
      </div>

      {/* Verification Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Verification Progress
          </CardTitle>
          <CardDescription>
            {metrics.verificationScore} of {metrics.totalChecks} verification steps completed
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Overall Progress</span>
              <span className="text-sm text-muted-foreground">
                {Math.round(metrics.progressPercentage)}%
              </span>
            </div>
            <Progress value={metrics.progressPercentage} className="w-full" />
            
            <div className="flex items-center gap-2">
              <Badge className={getTrustLevelColor(metrics.trustLevel)}>
                {metrics.trustLevel.replace('_', ' ')}
              </Badge>
              {metrics.isFullyVerified && (
                <Badge variant="outline" className="text-green-600 border-green-600">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Fully Verified
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Verification Steps */}
      <div className="grid gap-4">
        {/* Email Verification */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Email Verification
              </div>
              {getStatusIcon(verification.emailVerified)}
            </CardTitle>
            <CardDescription>
              Verify your email address to secure your account
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">
                  Status: {getStatusText(verification.emailVerified)}
                </p>
                <p className="text-sm text-muted-foreground">
                  Email: {session.user.email}
                </p>
              </div>
              {!verification.emailVerified && (
                <Button 
                  onClick={() => submitVerification('email_verification')}
                  disabled={isSubmitting}
                >
                  Verify Email
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Phone Verification */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Phone className="h-5 w-5" />
                Phone Verification
              </div>
              {getStatusIcon(verification.phoneVerified)}
            </CardTitle>
            <CardDescription>
              Add and verify your phone number for enhanced security
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">
                  Status: {getStatusText(verification.phoneVerified)}
                </p>
              </div>
              {!verification.phoneVerified && (
                <Button 
                  onClick={() => submitVerification('phone_verification')}
                  disabled={isSubmitting}
                >
                  Verify Phone
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Portfolio Verification */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Portfolio Verification
              </div>
              {getStatusIcon(verification.portfolioVerified)}
            </CardTitle>
            <CardDescription>
              Verify your work samples and portfolio links
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">
                  Status: {getStatusText(verification.portfolioVerified)}
                </p>
              </div>
              {!verification.portfolioVerified && (
                <Button 
                  onClick={() => submitVerification('portfolio_verification')}
                  disabled={isSubmitting}
                >
                  Verify Portfolio
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* ID Verification */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Identity Verification
              </div>
              {getStatusIcon(verification.idVerification)}
            </CardTitle>
            <CardDescription>
              Verify your identity with government-issued ID
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">
                  Status: {getStatusText(verification.idVerification)}
                </p>
              </div>
              {verification.idVerification === 'UNVERIFIED' && (
                <Button 
                  onClick={() => submitVerification('id_verification')}
                  disabled={isSubmitting}
                >
                  Start ID Verification
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Benefits */}
      <Card>
        <CardHeader>
          <CardTitle>Verification Benefits</CardTitle>
          <CardDescription>
            Why complete your verification?
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm">Higher trust rating and visibility</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm">Access to premium projects</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm">Faster payment processing</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm">Enhanced security features</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
