import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    console.log("🔍 Debug: Session user ID:", session.user.id)
    console.log("🔍 Debug: Session user email:", session.user.email)
    console.log("🔍 Debug: Session user name:", session.user.name)

    // Check if the session user exists in the database
    const sessionUser = await prisma.user.findUnique({
      where: { id: session.user.id }
    })

    console.log("🔍 Debug: Session user exists in DB:", !!sessionUser)

    // Get all users in the database
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true
      }
    })

    console.log("🔍 Debug: Total users in DB:", allUsers.length)

    return NextResponse.json({
      sessionUser: {
        id: session.user.id,
        email: session.user.email,
        name: session.user.name,
        existsInDB: !!sessionUser
      },
      allUsers: allUsers,
      totalUsers: allUsers.length
    })

  } catch (error) {
    console.error("Debug users error:", error)
    return NextResponse.json(
      { error: "Failed to fetch debug info" },
      { status: 500 }
    )
  }
}
