import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import {
  ArrowRight,
  BarChart3,
  Calendar,
  MessageSquare,
  Settings,
  Shield,
  Smartphone,
  Users,
  Zap,
  CheckCircle,
  Star,
  Play,
  Globe,
  Lock
} from "lucide-react"

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="sticky top-0 z-50 bg-white border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">Clever meet</h1>
            </div>

            <div className="hidden md:flex items-center space-x-8">
              <Link href="#valuable-feature" className="text-gray-600 hover:text-gray-900 text-sm font-medium transition-colors">
                Features
              </Link>
              <Link href="#benefits" className="text-gray-600 hover:text-gray-900 text-sm font-medium transition-colors">
                Benefits
              </Link>
              <Link href="#pricing" className="text-gray-600 hover:text-gray-900 text-sm font-medium transition-colors">
                Pricing
              </Link>
              <div className="relative group">
                <button className="text-gray-600 hover:text-gray-900 text-sm font-medium transition-colors">
                  Pages
                </button>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <Link href="/auth/signin">
                <Button variant="outline" className="bg-black text-white hover:bg-gray-800 border-black rounded-full px-6">
                  Get Active
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative pt-20 pb-16 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-blue-100 rounded-full opacity-60"></div>
        <div className="absolute top-40 right-20 w-16 h-16 bg-purple-100 rounded-full opacity-60"></div>
        <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-green-100 rounded-full opacity-60"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            {/* Badge */}
            <div className="inline-flex items-center gap-2 bg-gray-100 rounded-full px-4 py-2 mb-8">
              <Badge variant="secondary" className="bg-black text-white text-xs">New</Badge>
              <Zap className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-gray-700">Smart AI Features</span>
            </div>

            {/* Main Heading */}
            <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold text-gray-900 mb-6 leading-tight">
              Great Collaborations Start with a call
            </h1>

            {/* Subtitle */}
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-12 leading-relaxed">
              MeetBoard connects clients and freelancers through seamless meetings, project boards, and built-in communication — all in one place.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
              <Link href="#valuable-feature">
                <Button size="lg" className="bg-black text-white hover:bg-gray-800 px-8 py-4 text-lg rounded-full font-medium">
                  Explore Active
                </Button>
              </Link>
              <Link href="/auth/signin">
                <Button variant="outline" size="lg" className="px-8 py-4 text-lg rounded-full border-gray-300 hover:bg-gray-50 font-medium">
                  Request a Demo
                </Button>
              </Link>
            </div>

            {/* Company Logos */}
            <div className="mt-16">
              <div className="text-sm text-gray-500 mb-8 text-center font-medium">
                Currently Supported
              </div>
              <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
                {/* Company logos placeholder - you can replace with actual logos */}
                <div className="w-24 h-12 bg-gray-200 rounded flex items-center justify-center">
                  <span className="text-xs text-gray-500">Logo 1</span>
                </div>
                <div className="w-24 h-12 bg-gray-200 rounded flex items-center justify-center">
                  <span className="text-xs text-gray-500">Logo 2</span>
                </div>
                <div className="w-24 h-12 bg-gray-200 rounded flex items-center justify-center">
                  <span className="text-xs text-gray-500">Logo 3</span>
                </div>
                <div className="w-24 h-12 bg-gray-200 rounded flex items-center justify-center">
                  <span className="text-xs text-gray-500">Logo 4</span>
                </div>
                <div className="w-24 h-12 bg-gray-200 rounded flex items-center justify-center">
                  <span className="text-xs text-gray-500">Logo 5</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Valuable Features Section */}
      <section id="valuable-feature" className="py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-4">
              Valuable Features
            </h2>
            <h3 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
              meetings predeadlines schedule just for your comfort
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <Card className="p-8 text-center hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <BarChart3 className="w-8 h-8 text-blue-600" />
                </div>
                <h4 className="text-xl font-semibold text-gray-900 mb-4">Real Time Analytics</h4>
                <p className="text-gray-600">Monitor result performance with real time insights.</p>
              </CardContent>
            </Card>

            {/* Feature 2 */}
            <Card className="p-8 text-center hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Calendar className="w-8 h-8 text-green-600" />
                </div>
                <h4 className="text-xl font-semibold text-gray-900 mb-4">Content Scheduling</h4>
                <p className="text-gray-600">Plan and schedule content across all platforms.</p>
              </CardContent>
            </Card>

            {/* Feature 3 */}
            <Card className="p-8 text-center hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Users className="w-8 h-8 text-purple-600" />
                </div>
                <h4 className="text-xl font-semibold text-gray-900 mb-4">Team Collaboration</h4>
                <p className="text-gray-600">Manage and improve campaigns efficiently.</p>
              </CardContent>
            </Card>

            {/* Feature 4 */}
            <Card className="p-8 text-center hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <MessageSquare className="w-8 h-8 text-orange-600" />
                </div>
                <h4 className="text-xl font-semibold text-gray-900 mb-4">Streamline Communication</h4>
                <p className="text-gray-600">Manage conversations for smooth coordination.</p>
              </CardContent>
            </Card>

            {/* Feature 5 */}
            <Card className="p-8 text-center hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Settings className="w-8 h-8 text-red-600" />
                </div>
                <h4 className="text-xl font-semibold text-gray-900 mb-4">Customizable Dashboards</h4>
                <p className="text-gray-600">Adapt your workspace to highlight critical metrics.</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section id="benefits" className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-4">
              Benefits
            </h2>
            <h3 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
              Unlock a New Era of Operational Excellence and Innovation
            </h3>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Unlock operational excellence and innovation with our advanced tools and streamlined processes.
            </p>
          </div>

          {/* Benefits Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {/* Benefit 1 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Users className="w-8 h-8 text-blue-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-4">Best work force</h4>
              <p className="text-gray-600">An expert team ready to tackle your challenges with innovative solutions and proven strategies.</p>
            </div>

            {/* Benefit 2 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Calendar className="w-8 h-8 text-green-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-4">Just a meet</h4>
              <p className="text-gray-600">Scale your business effortlessly with our SaaS, designed to grow alongside your evolving needs.</p>
            </div>

            {/* Benefit 3 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Settings className="w-8 h-8 text-purple-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-4">Dashboard with profound plugins</h4>
              <p className="text-gray-600">Customize the platform to perfectly align with your business&apos;s unique requirements and goals.</p>
            </div>

            {/* Benefit 4 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Zap className="w-8 h-8 text-orange-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-4">Maximum Efficiency</h4>
              <p className="text-gray-600">Maximize efficiency with integrated solutions that eliminate bottlenecks, saving time and costs.</p>
            </div>

            {/* Benefit 5 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Smartphone className="w-8 h-8 text-red-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-4">User Friendly</h4>
              <p className="text-gray-600">A simple and accessible interface for users of all skill levels, making it easy to find what you need.</p>
            </div>

            {/* Benefit 6 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Shield className="w-8 h-8 text-gray-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-4">Security You Can Trust</h4>
              <p className="text-gray-600">Protect sensitive data with industry leading security to prevent unauthorized breaches.</p>
            </div>
          </div>

          {/* Stats Section */}
          <div className="bg-gray-50 rounded-3xl p-12">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold text-gray-900 mb-4">The Best Fit Freelancers & Startups</h3>
              <p className="text-xl text-gray-600">We prioritize your success by offering tailored solutions designed to meet your unique needs.</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div>
                <div className="text-4xl font-bold text-gray-900 mb-2">100+</div>
                <div className="text-gray-600">Positive Reviews</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-gray-900 mb-2">78</div>
                <div className="text-gray-600">Users Satisfied</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-gray-900 mb-2">4.9/5</div>
                <div className="text-gray-600">Overall Ratings</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How to Use Section */}
      <section className="py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-4">
              How to Use?
            </h2>
            <h3 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
              Simple Steps to Get Started
            </h3>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Experience CRM at its finest with smooth integration, insightful analytics, and a user experience built for the future.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Step 1 */}
            <div className="text-center">
              <div className="relative mb-6">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                  <Users className="w-8 h-8 text-blue-600" />
                </div>
                <div className="absolute -top-2 -right-2 bg-black text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold">
                  1
                </div>
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-4">Sign Up</h4>
              <p className="text-gray-600">Register in just a few minutes and kickstart your CRM journey.</p>
            </div>

            {/* Step 2 */}
            <div className="text-center">
              <div className="relative mb-6">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <Users className="w-8 h-8 text-green-600" />
                </div>
                <div className="absolute -top-2 -right-2 bg-black text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold">
                  2
                </div>
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-4">Find Freelancers</h4>
              <p className="text-gray-600">Easily upload existing customer data for seamless access.</p>
            </div>

            {/* Step 3 */}
            <div className="text-center">
              <div className="relative mb-6">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                  <Calendar className="w-8 h-8 text-purple-600" />
                </div>
                <div className="absolute -top-2 -right-2 bg-black text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold">
                  3
                </div>
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-4">Book a call</h4>
              <p className="text-gray-600">Personalize the CRM features to align with your processes.</p>
            </div>

            {/* Step 4 */}
            <div className="text-center">
              <div className="relative mb-6">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto">
                  <CheckCircle className="w-8 h-8 text-orange-600" />
                </div>
                <div className="absolute -top-2 -right-2 bg-black text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold">
                  ✓
                </div>
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-4">Moodboard</h4>
              <p className="text-gray-600">Finalize the setup & start managing your customers right away.</p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-24 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-4">
              Common Question
            </h2>
            <h3 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
              Frequently Asked Questions
            </h3>
          </div>

          <div className="space-y-6">
            <div className="border border-gray-200 rounded-lg p-6">
              <h4 className="text-xl font-semibold text-gray-900 mb-4">How Clevermeet works</h4>
              <p className="text-gray-600">
                Clevermeet is the platform for freelancer, Clients, Early Start-ups that will help there workflow fast,
                Reliable and safe that means no risk of ghosting, payments & lack of Communication
              </p>
            </div>

            <div className="border border-gray-200 rounded-lg p-6">
              <h4 className="text-xl font-semibold text-gray-900 mb-4">What kind of customer support do you provide?</h4>
              <p className="text-gray-600">
                We provide 24/7 customer support through multiple channels including email, chat, and phone support.
              </p>
            </div>

            <div className="border border-gray-200 rounded-lg p-6">
              <h4 className="text-xl font-semibold text-gray-900 mb-4">How to use Clevermeet</h4>
              <p className="text-gray-600">
                Simply sign up, create your profile, find freelancers or clients, and start collaborating through our integrated platform.
              </p>
            </div>

            <div className="border border-gray-200 rounded-lg p-6">
              <h4 className="text-xl font-semibold text-gray-900 mb-4">What&apos;s the benefit of using Clevermeet as a freelancer</h4>
              <p className="text-gray-600">
                Get access to quality clients, secure payments, professional project management tools, and a platform that protects you from ghosting.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-24 bg-black text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-sm font-semibold text-gray-400 uppercase tracking-wide mb-4">
            Elevate Your Business
          </h2>
          <h3 className="text-4xl sm:text-5xl font-bold mb-6">
            Ready to Transform<br />
            Your management journey<br />
            book a call?
          </h3>
          <p className="text-xl text-gray-300 mb-8">
            Sign up today and see the difference Active can make for your business.
          </p>
          <Link href="/auth/signup">
            <Button size="lg" className="bg-white text-black hover:bg-gray-100 px-8 py-4 text-lg rounded-full font-medium">
              Get Started
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-2xl font-bold mb-4">Clever meet</h3>
              <p className="text-gray-400 mb-6">
                Streamline workflows and grow your business with effective lead management.
              </p>
              <p className="text-gray-400"><EMAIL></p>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Features</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="#benefits" className="hover:text-white transition-colors">Benefits</Link></li>
                <li><Link href="#why-choose-us" className="hover:text-white transition-colors">Why Choose Us</Link></li>
                <li><Link href="#how-to-use" className="hover:text-white transition-colors">How To Use</Link></li>
                <li><Link href="#pricing" className="hover:text-white transition-colors">Pricing</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Pages</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/" className="hover:text-white transition-colors">Homepage</Link></li>
                <li><Link href="/contact" className="hover:text-white transition-colors">Contact</Link></li>
                <li><Link href="/404" className="hover:text-white transition-colors">404 Page</Link></li>
                <li><Link href="/terms" className="hover:text-white transition-colors">Terms & Conditions</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">Copyright Active SaaS. All right reserved.</p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link href="#" className="text-gray-400 hover:text-white transition-colors">Twitter</Link>
              <Link href="#" className="text-gray-400 hover:text-white transition-colors">Instagram</Link>
              <Link href="#" className="text-gray-400 hover:text-white transition-colors">LinkedIn</Link>
              <Link href="#" className="text-gray-400 hover:text-white transition-colors">Dribbble</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
