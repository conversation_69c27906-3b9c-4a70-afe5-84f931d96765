import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { cache, cacheKeys } from "@/lib/cache"

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userId = session.user.id

    // Start a transaction to delete all user data
    await prisma.$transaction(async (tx) => {
      // Delete user verification records
      await tx.userVerification.deleteMany({
        where: { userId }
      })

      // Delete notifications
      await tx.notification.deleteMany({
        where: { userId }
      })

      // Delete freelancer profile if exists
      await tx.freelancerProfile.deleteMany({
        where: { userId }
      })

      // Delete client profile if exists
      await tx.clientProfile.deleteMany({
        where: { userId }
      })

      // Delete meetings where user is client or freelancer
      await tx.meeting.deleteMany({
        where: {
          OR: [
            { clientId: userId },
            { freelancerId: userId }
          ]
        }
      })

      // Delete payments where user is client or freelancer
      await tx.payment.deleteMany({
        where: {
          OR: [
            { clientId: userId },
            { freelancerId: userId }
          ]
        }
      })

      // Delete projects where user is client or freelancer
      await tx.project.deleteMany({
        where: {
          OR: [
            { clientId: userId },
            { freelancerId: userId }
          ]
        }
      })

      // Finally, delete the user
      await tx.user.delete({
        where: { id: userId }
      })
    })

    // Clear all related cache entries
    cache.delete(cacheKeys.userProfile(userId))
    cache.delete(cacheKeys.notifications(userId))
    cache.delete(cacheKeys.dashboardStats(userId, session.user.role))
    cache.delete(cacheKeys.meetings(userId))

    return NextResponse.json({ 
      message: "Account deleted successfully"
    })
  } catch (error) {
    console.error("Error deleting account:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
